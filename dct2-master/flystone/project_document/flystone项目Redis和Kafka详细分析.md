# Flystone项目Redis和Kafka详细分析

## 1. Redis键详细分析

### 1.1 设备数据相关Redis键

| Redis键 | 完整格式 | 数据类型 | 存储内容 | 过期时间 | 使用位置 | 业务逻辑 |
|---------|----------|----------|----------|----------|----------|----------|
| `RECEIVE_COMPLETE_DATA` | `RECEIVE_COMPLETE_DATA-{sn}-{commandNum}` | String | 接收到的消息时间戳 | 3天 | DataHandleService.receiveMessage() | 记录已接收到的设备数据，防止重复处理 |
| `MAX_RECEIVE_NUM` | `MAX_RECEIVE_NUM-{sn}` | Integer | 最大接收到的编号 | 永久 | DataHandleService.renewMaxReceiveNum() | 记录已接收到的最大数据编号，用于数据完整性检查 |
| `MAX_CHECK_NUM` | `MAX_CHECK_NUM-{sn}` | Integer | 最大检查编号 | 永久 | DataHandleService.checkCompleteThread() | 记录已检查过的最大数据编号，用于检测丢包 |
| `LOSE_DATA_LIST` | `LOSE_DATA_LIST-{sn}` | List | 丢失的数据包编号列表 | 永久 | DataHandleService.checkCompleteThread() | 存储丢失的数据包编号，用于补数据 |
| `CHECK_INTERVAL_NUM` | `CHECK_INTERVAL_NUM` | Integer | 检查间隔数量 | 永久 | DataHandleService.checkDataComplete() | 配置最大接收编号与最大检查编号的差值阈值，超过此值触发检查 |

**处理逻辑详解**：
1. **数据接收**：
   - 当接收到设备数据时，首先将数据包的时间戳存入 `RECEIVE_COMPLETE_DATA-{sn}-{commandNum}` 键，并设置3天过期时间
   - 将数据发送到Kafka的 `DEVICE_DATA_TOPIC`
   - 更新 `MAX_RECEIVE_NUM-{sn}` 键为当前接收到的最大编号
   
2. **数据完整性检查**：
   - 检查当前接收编号与最大检查编号的差值，如果超过 `CHECK_INTERVAL_NUM` 设定的阈值（默认100）
   - 如果已有检查锁（`LOSE_DATA_CHECK-{sn}`），表示已在检查，则直接返回
   - 否则，加锁并在后台线程中执行检查
   
3. **数据完整性检查线程**：
   - 从最大检查编号到最大接收编号之间遍历每个编号
   - 对每个编号，检查 `RECEIVE_COMPLETE_DATA-{sn}-{commandNum}` 是否存在
   - 如果不存在，则将该编号加入 `LOSE_DATA_LIST-{sn}` 列表中
   - 更新 `MAX_CHECK_NUM-{sn}` 为当前检查的最大编号
   - 发送UDP补数据请求
   
4. **补数据处理**：
   - 从 `LOSE_DATA_LIST-{sn}` 列表中取出丢失的数据编号（最多240个）
   - 将这些编号发送给船舶设备，请求重新发送数据

### 1.2 快照数据相关Redis键

| Redis键 | 完整格式 | 数据类型 | 存储内容 | 过期时间 | 使用位置 | 业务逻辑 |
|---------|----------|----------|----------|----------|----------|----------|
| `SNAPSHOT_RECEIVE_TEMPORARY` | `SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}` | Set | 接收到的TransferPackage对象集合 | 4小时 | SnapshotHandleService.receiveMessage() | 临时存储接收到的快照数据包，用于后续合并 |
| `SNAPSHOT_NEWEST_TIME` | `SNAPSHOT_NEWEST_TIME-{sn}-{commandNum}` | Long | 最新接收时间戳 | 4小时 | SnapshotHandleService.receiveMessage() | 记录最新接收到快照数据的时间，用于判断是否还在接收中 |
| `SNAPSHOT_NEWEST_REPAIR_TIME` | `SNAPSHOT_NEWEST_REPAIR_TIME-{sn}-{commandNum}` | Long | 最新补数据时间戳 | 4小时 | SnapshotHandleService | 记录最新补数据的时间，用于判断是否需要继续等待补数据 |
| `LATEST_PICTURE_DATE` | `LATEST_PICTURE_DATE-{sn}-{channelCode}` | String | 最新快照时间 | 永久 | SnapshotHandleService.renewLatestPictureTime() | 记录指定设备最新快照的时间，用于后续查询 |

**处理逻辑详解**：
1. **快照数据接收**：
   - 当接收到快照数据时，首先将数据包对象添加到 `SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}` 集合中
   - 更新 `SNAPSHOT_NEWEST_TIME-{sn}-{commandNum}` 为当前时间戳
   - 启动检查线程，验证快照数据是否完整
   
2. **快照数据完整性检查**：
   - 从 `SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}` 获取已接收的数据包集合
   - 如果集合大小等于或超过 `unpackingTotal`（总包数），则认为数据已完整接收
   - 否则，检查最近接收时间（`SNAPSHOT_NEWEST_TIME`）和最近补数据时间（`SNAPSHOT_NEWEST_REPAIR_TIME`）
   - 如果距离最近接收或补数据时间不超过阈值（25秒或20秒），则继续等待接收
   - 否则，确定为丢包，启动补数据流程
   
3. **快照数据合并与处理**：
   - 当快照数据完整接收后，合并所有数据包的消息内容
   - 更新 `LATEST_PICTURE_DATE-{sn}-{channelCode}` 为当前快照的时间
   - 发送数据到Kafka的 `SNAPSHOT_LATEST_TOPIC`
   - 向船舶设备发送接收成功的确认

4. **快照数据补包**：
   - 计算丢失的包序号，发送补包请求
   - 更新 `SNAPSHOT_NEWEST_REPAIR_TIME-{sn}-{commandNum}` 为当前时间戳

### 1.3 同步数据相关Redis键

| Redis键 | 完整格式 | 数据类型 | 存储内容 | 过期时间 | 使用位置 | 业务逻辑 |
|---------|----------|----------|----------|----------|----------|----------|
| `SHIP_SYNC_DATA` | `SHIP_SYNC_DATA-{sn}_{commandNum}` | String | TransferPackage的JSON字符串 | 2小时 | SyncHandleService.receiveMessage() | 存储同步数据，防止重复处理 |
| `SHIP_SYNC_SUCCESS` | `SHIP_SYNC_SUCCESS-{sn}_{commandNum}` | String | TransferPackage的JSON字符串 | 2小时 | SyncHandleService.receiveSuccess() | 标记同步数据已成功接收确认 |

**处理逻辑详解**：
1. **同步数据接收**：
   - 当接收到同步数据时，先发送接收成功的确认
   - 检查 `SHIP_SYNC_DATA-{sn}_{commandNum}` 是否已存在，如存在则直接返回（防止重复处理）
   - 通过 LaputaApi 更新数据库
   - 将同步数据的JSON字符串存入 `SHIP_SYNC_DATA-{sn}_{commandNum}`，设置2小时过期
   
2. **同步数据成功确认**：
   - 当接收到同步数据成功确认时，将确认信息存入 `SHIP_SYNC_SUCCESS-{sn}_{commandNum}`，设置2小时过期
   
3. **原始数据处理**：
   - 当接收到原始数据时，将数据发送到Kafka的 `RAW_DATA_TOPIC`

4. **岸上同步数据到船舶**：
   - 从Kafka的 `SHORE_SYNC_DATA_TOPIC` 消费消息
   - 将消息通过UDP发送给船舶设备
   - 启动后台线程，15秒后检查 `SHIP_SYNC_SUCCESS-{sn}_{commandNum}` 是否存在
   - 如果不存在，则重新发送消息

### 1.4 配置相关Redis键

| Redis键 | 完整格式 | 数据类型 | 存储内容 | 过期时间 | 使用位置 | 业务逻辑 |
|---------|----------|----------|----------|----------|----------|----------|
| `ALL_ENABLE_SHIP_SN` | `ALL_ENABLE_SHIP_SN` | Set | 所有启用的船只SN号集合 | 永久 | ShipService.queryEnableSnList() | 存储所有启用的船只SN号，用于验证数据源的有效性 |
| `ALL_ENABLE_DEVICE_CODE` | `ALL_ENABLE_DEVICE_CODE` | Set | 所有启用的设备代码集合 | 永久 | DeviceService.queryEnableCodeList() | 存储所有启用的设备代码，用于验证设备的有效性 |

**处理逻辑详解**：
1. **船只SN验证**：
   - 当接收到数据包时，从 `ALL_ENABLE_SHIP_SN` 获取所有有效的船只SN号
   - 检查接收到的数据包的SN是否在有效列表中，如果不在则丢弃数据
   
2. **设备代码验证**：
   - 当接收到设备数据时，从 `ALL_ENABLE_DEVICE_CODE` 获取所有有效的设备代码
   - 检查接收到的数据包的设备代码（`{sn}_{deviceCode}`）是否在有效列表中，如果不在则丢弃数据

## 2. Kafka主题详细分析

| Kafka主题 | 生产者 | 消费者 | 消息内容 | 业务逻辑 |
|-----------|--------|--------|----------|----------|
| `DEVICE_DATA_TOPIC` | SendService.sendData2Kafka() | 其他系统组件 | TransferPackage对象的JSON | 传输船舶设备数据，用于后续的数据处理和分析 |
| `SNAPSHOT_LATEST_TOPIC` | SendService.sendSnapshot2Kafka() | 其他系统组件 | TransferPackage对象的JSON（message字段被清空） | 通知系统有新的快照数据，但不包含具体数据内容 |
| `RAW_DATA_TOPIC` | SendService.sendRawData2Kafka() | 其他系统组件 | TransferPackage对象的JSON | 传输船舶原始数据，用于日志记录和审计 |
| `SHORE_SYNC_DATA_TOPIC` | 其他系统组件 | ConsumerService.syncDataConsumer() | KafkaMessage对象的JSON | 接收需要同步到船舶的数据，并通过UDP发送给船舶 |

**处理逻辑详解**：
1. **设备数据流程**：
   - 船舶设备通过UDP发送数据到Flystone服务
   - DataHandleService处理数据，并通过SendService.sendData2Kafka()将数据发送到`DEVICE_DATA_TOPIC`
   - 其他系统组件消费`DEVICE_DATA_TOPIC`的消息，进行后续处理
   
2. **快照数据流程**：
   - 船舶设备通过UDP发送快照数据到Flystone服务
   - SnapshotHandleService处理数据，将快照文件保存到磁盘
   - 通过SendService.sendSnapshot2Kafka()发送通知消息（不含具体数据）到`SNAPSHOT_LATEST_TOPIC`
   - 其他系统组件消费`SNAPSHOT_LATEST_TOPIC`的消息，获知有新的快照数据可用
   
3. **原始数据流程**：
   - 船舶设备通过UDP发送原始数据到Flystone服务
   - SyncHandleService处理数据，并通过SendService.sendRawData2Kafka()将数据发送到`RAW_DATA_TOPIC`
   - 其他系统组件消费`RAW_DATA_TOPIC`的消息，进行日志记录和审计
   
4. **岸上同步数据流程**：
   - 其他系统组件生产消息到`SHORE_SYNC_DATA_TOPIC`
   - ConsumerService.syncDataConsumer()消费消息
   - 将消息转换为KafkaMessage对象，通过SyncHandleService.sendSync2Ship()发送给船舶设备
   - 等待船舶设备的确认响应，如15秒内未收到则重新发送

## 3. 数据流向与业务流程

### 3.1 船舶设备数据上报流程

```mermaid
sequenceDiagram
    participant Ship as 船舶设备
    participant UDPServer as UDP服务
    participant DataService as DataHandleService
    participant Redis as Redis缓存
    participant Kafka as Kafka消息队列
    participant Other as 其他系统组件
    
    Ship->>UDPServer: 发送设备数据(TransferPackage)
    UDPServer->>DataService: 解析并分发数据
    DataService->>Redis: 存储数据时间戳(RECEIVE_COMPLETE_DATA-{sn}-{commandNum})
    DataService->>Redis: 更新最大接收编号(MAX_RECEIVE_NUM-{sn})
    DataService->>Kafka: 发送数据到DEVICE_DATA_TOPIC
    DataService->>Redis: 检查数据完整性(MAX_CHECK_NUM-{sn})
    alt 数据不完整
        DataService->>Redis: 记录丢失数据编号(LOSE_DATA_LIST-{sn})
        DataService->>Ship: 发送补数据请求
    end
    Kafka->>Other: 消费设备数据
```

**详细说明**：
1. 船舶设备通过UDP发送设备数据包到Flystone服务
2. UDP服务器接收数据，通过ConnectService解析并分发到DataHandleService
3. DataHandleService处理流程：
   - 将数据时间戳存入Redis (`RECEIVE_COMPLETE_DATA-{sn}-{commandNum}`)，设置3天过期
   - 更新最大接收编号 (`MAX_RECEIVE_NUM-{sn}`)
   - 发送数据到Kafka (`DEVICE_DATA_TOPIC`)
   - 检查数据完整性：比较最大接收编号与最大检查编号的差值
   - 如果差值超过阈值（默认100），启动检查线程
4. 检查线程遍历所有缺失的编号，记录到丢失数据列表 (`LOSE_DATA_LIST-{sn}`)
5. 向船舶发送补数据请求
6. 其他系统组件从Kafka消费设备数据，进行后续处理

### 3.2 快照数据处理流程

```mermaid
sequenceDiagram
    participant Ship as 船舶设备
    participant UDPServer as UDP服务
    participant SnapshotService as SnapshotHandleService
    participant Redis as Redis缓存
    participant Kafka as Kafka消息队列
    participant Other as 其他系统组件
    
    Ship->>UDPServer: 发送快照数据(分包)
    UDPServer->>SnapshotService: 解析并分发数据
    SnapshotService->>Redis: 存储数据包(SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum})
    SnapshotService->>Redis: 更新最新接收时间(SNAPSHOT_NEWEST_TIME-{sn}-{commandNum})
    SnapshotService->>SnapshotService: 检查数据完整性
    alt 数据不完整
        SnapshotService->>Ship: 发送补数据请求
        SnapshotService->>Redis: 更新补数据时间(SNAPSHOT_NEWEST_REPAIR_TIME-{sn}-{commandNum})
    else 数据完整
        SnapshotService->>SnapshotService: 合并数据包，保存快照文件
        SnapshotService->>Redis: 更新最新快照时间(LATEST_PICTURE_DATE-{sn}-{channelCode})
        SnapshotService->>Kafka: 发送通知到SNAPSHOT_LATEST_TOPIC
        SnapshotService->>Ship: 发送接收成功确认
    end
    Kafka->>Other: 消费快照通知
```

**详细说明**：
1. 船舶设备通过UDP发送快照数据包（分多个包）到Flystone服务
2. UDP服务器接收数据，通过ConnectService解析并分发到SnapshotHandleService
3. SnapshotHandleService处理流程：
   - 将数据包对象添加到Redis集合 (`SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}`)，设置4小时过期
   - 更新最新接收时间 (`SNAPSHOT_NEWEST_TIME-{sn}-{commandNum}`)
   - 启动检查线程，验证数据完整性
4. 检查线程判断是否收到所有数据包：
   - 如果已收到所有包，则合并数据，保存快照文件
   - 更新最新快照时间 (`LATEST_PICTURE_DATE-{sn}-{channelCode}`)
   - 发送通知消息到Kafka (`SNAPSHOT_LATEST_TOPIC`)
   - 向船舶发送接收成功确认
5. 如果数据不完整：
   - 计算丢失的包序号，发送补包请求
   - 更新补数据时间 (`SNAPSHOT_NEWEST_REPAIR_TIME-{sn}-{commandNum}`)
   - 继续等待接收
6. 其他系统组件从Kafka消费快照通知，获知有新的快照可用

### 3.3 同步数据处理流程

```mermaid
sequenceDiagram
    participant Ship as 船舶设备
    participant UDPServer as UDP服务
    participant SyncService as SyncHandleService
    participant Redis as Redis缓存
    participant Kafka as Kafka消息队列
    participant Laputa as LaputaApi
    participant Other as 其他系统组件
    
    alt 船舶到岸上同步
        Ship->>UDPServer: 发送同步数据
        UDPServer->>SyncService: 解析并分发数据
        SyncService->>Ship: 发送接收成功确认
        SyncService->>Redis: 检查是否处理过(SHIP_SYNC_DATA-{sn}_{commandNum})
        alt 未处理过
            SyncService->>Laputa: 更新数据库
            SyncService->>Redis: 存储同步数据(SHIP_SYNC_DATA-{sn}_{commandNum})
        end
    else 岸上到船舶同步
        Other->>Kafka: 发送同步数据到SHORE_SYNC_DATA_TOPIC
        Kafka->>SyncService: 消费同步数据
        SyncService->>Ship: 发送同步数据
        Ship->>UDPServer: 发送接收成功确认
        UDPServer->>SyncService: 处理确认
        SyncService->>Redis: 存储确认信息(SHIP_SYNC_SUCCESS-{sn}_{commandNum})
        alt 未收到确认
            SyncService->>Ship: 15秒后重新发送
        end
    end
```

**详细说明**：
1. **船舶到岸上同步流程**：
   - 船舶设备通过UDP发送同步数据到Flystone服务
   - UDP服务器接收数据，通过ConnectService解析并分发到SyncHandleService
   - SyncHandleService处理流程：
     - 发送接收成功确认给船舶
     - 检查Redis中是否已处理过此数据 (`SHIP_SYNC_DATA-{sn}_{commandNum}`)
     - 如果未处理过，则通过LaputaApi更新数据库
     - 将同步数据存入Redis (`SHIP_SYNC_DATA-{sn}_{commandNum}`)，设置2小时过期

2. **岸上到船舶同步流程**：
   - 其他系统组件发送同步数据到Kafka (`SHORE_SYNC_DATA_TOPIC`)
   - ConsumerService消费消息，调用SyncHandleService.sendSync2Ship()
   - 通过UDP将同步数据发送给船舶设备
   - 启动后台线程，15秒后检查是否收到确认
   - 船舶收到数据后，发送确认消息
   - SyncHandleService接收确认，存储确认信息 (`SHIP_SYNC_SUCCESS-{sn}_{commandNum}`)
   - 如果15秒内未收到确认，则重新发送数据

### 3.4 原始数据处理流程

```mermaid
sequenceDiagram
    participant Ship as 船舶设备
    participant UDPServer as UDP服务
    participant SyncService as SyncHandleService
    participant Kafka as Kafka消息队列
    participant Other as 其他系统组件
    
    Ship->>UDPServer: 发送原始数据
    UDPServer->>SyncService: 解析并分发数据
    SyncService->>Kafka: 发送数据到RAW_DATA_TOPIC
    Kafka->>Other: 消费原始数据
```

**详细说明**：
1. 船舶设备通过UDP发送原始数据到Flystone服务
2. UDP服务器接收数据，通过ConnectService解析并分发到SyncHandleService
3. SyncHandleService处理流程：
   - 发送数据到Kafka (`RAW_DATA_TOPIC`)
4. 其他系统组件从Kafka消费原始数据，进行日志记录和审计