# flystone项目业务流程分析

## 项目概述

Flystone是一个基于Spring Boot的应用程序，主要负责与船舶设备的数据通信。它使用UDP协议与船舶设备通信，并通过Kafka消息队列与其他系统组件进行数据交换，同时使用Redis进行数据缓存。

## 核心业务流程

以下是Flystone项目的核心业务流程，重点关注Redis和Kafka的使用以及数据流向。

### Mermaid流程图

```mermaid
flowchart TD
    subgraph 船舶设备
        Ship[船舶设备] --> |UDP数据包| UDPServer
    end
    
    subgraph Flystone服务
        UDPServer[UDP服务器UdpServer.java] --> |接收数据| Handler[UDP处理器NormalUdpClientHandler.java]
        Handler --> |分析数据包| ConnectService[连接服务ConnectService.java]
        
        ConnectService --> |设备数据| DataHandleService[设备数据处理DataHandleService.java]
        ConnectService --> |快照数据| SnapshotHandleService[快照数据处理SnapshotHandleService.java]
        ConnectService --> |同步数据| SyncHandleService[同步数据处理SyncHandleService.java]
        
        DataHandleService --> |存储数据| Redis[(Redis缓存)]
        SnapshotHandleService --> |存储快照| Redis
        SyncHandleService --> |存储同步数据| Redis
        
        DataHandleService --> |发送设备数据| KafkaProducer[Kafka生产者SendService.java]
        SnapshotHandleService --> |发送快照数据| KafkaProducer
        SyncHandleService --> |发送原始数据| KafkaProducer
        
        KafkaProducer --> |DEVICE_DATA_TOPIC| Kafka[(Kafka消息队列)]
        KafkaProducer --> |SNAPSHOT_LATEST_TOPIC| Kafka
        KafkaProducer --> |RAW_DATA_TOPIC| Kafka
        
        Kafka --> |SHORE_SYNC_DATA_TOPIC| KafkaConsumer[Kafka消费者ConsumerService.java]
        KafkaConsumer --> |处理同步数据| SyncHandleService
        
        SyncHandleService --> |回传同步结果| SendService[发送服务SendService.java]
        SendService --> |UDP响应| Ship
    end
    
    subgraph 其他系统组件
        Kafka --> OtherSystems[其他系统组件]
        SyncHandleService --> |调用API| LaputaApi[LaputaApi远程服务]
    end
```

### 详细业务流程说明

按照数据流动顺序，以下是系统的核心业务流程：

#### 1. UDP数据接收与处理
- **文件路径**: `src/main/java/com/xhjt/flystone/server/UdpServer.java`
- **流程**:
  - 系统通过UDP服务器监听端口(10003)，接收来自船舶设备的数据包
  - `NormalUdpClientHandler.java` 处理接收到的UDP数据包
  - 数据包传递给 `ConnectService.analysisPackage()` 方法进行解析
  - `ConnectService.handleMessage()` 根据数据包类型分发到不同的处理服务

#### 2. 数据分类处理
根据数据包类型(packageType)，系统将数据分为三类：

1. **设备数据处理** (packageType = DEVICE_DATA)
   - **处理类**: `DataHandleService.java`
   - **功能**: 处理船舶设备传感器数据
   - **Redis Key**: `RECEIVE_COMPLETE_DATA-{sn}`, `MAX_RECEIVE_NUM-{sn}`, `MAX_CHECK_NUM-{sn}`
   - **Kafka Topic**: `DEVICE_DATA_TOPIC`

2. **快照数据处理** (packageType = SNAPSHOT_DATA)
   - **处理类**: `SnapshotHandleService.java`
   - **功能**: 处理船舶快照数据
   - **Redis Key**: `SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}`, `SNAPSHOT_NEWEST_TIME-{sn}-{commandNum}`
   - **Kafka Topic**: `SNAPSHOT_LATEST_TOPIC`

3. **同步数据处理** (packageType = DATA_SYNC)
   - **处理类**: `SyncHandleService.java`
   - **功能**: 处理需要与船舶同步的数据
   - **Redis Key**: `SHIP_SYNC_DATA-{sn}_{commandNum}`, `SHIP_SYNC_SUCCESS-{sn}_{commandNum}`
   - **Kafka Topic**: `RAW_DATA_TOPIC`

#### 3. Kafka消息消费
- **消费者类**: `ConsumerService.java`
- **监听Topic**: `SHORE_SYNC_DATA_TOPIC`
- **功能**: 接收岸上系统发送的需要同步到船舶的数据
- **处理流程**:
  1. 接收Kafka消息
  2. 转换为`KafkaMessage`对象
  3. 调用`SyncHandleService.sendSync2Ship()`发送同步数据到船舶
  4. 在Redis中记录同步状态，并设置过期时间(2小时)

#### 4. 数据同步和响应
- **核心类**: `SyncHandleService.java`, `SendService.java`
- **功能**:
  - 将Kafka中的同步数据通过UDP发送到船舶
  - 接收船舶的同步确认响应
  - 如果15秒内没有收到确认，会重新发送
  - 成功接收同步数据后，通过`LaputaApi`更新数据库

## 关键Redis键和Kafka主题

### Redis键
1. **设备数据相关**:
   - `RECEIVE_COMPLETE_DATA-{sn}`: 存储接收到的完整设备数据
   - `MAX_RECEIVE_NUM-{sn}`: 最大接收到的编号
   - `MAX_CHECK_NUM-{sn}`: 最大校验编号
   - `LOSE_DATA_LIST-{sn}`: 岸上丢失数据链表

2. **快照数据相关**:
   - `SNAPSHOT_RECEIVE_TEMPORARY-{sn}-{commandNum}`: 快照数据临时存储
   - `SNAPSHOT_NEWEST_TIME-{sn}-{commandNum}`: 快照数据编号的最新获取时间
   - `SNAPSHOT_NEWEST_REPAIR_TIME-{sn}-{commandNum}`: 快照数据最新补数据时间
   - `LATEST_PICTURE_DATE-{sn}`: 最新接收快照的时间

3. **同步数据相关**:
   - `SHIP_SYNC_DATA-{sn}_{commandNum}`: 船上同步数据
   - `SHIP_SYNC_SUCCESS-{sn}_{commandNum}`: 船上同步数据成功标志

4. **配置相关**:
   - `ALL_ENABLE_SHIP_SN`: 所有启用的船只SN号
   - `ALL_ENABLE_DEVICE_CODE`: 所有启用的设备

### Kafka主题
1. `DEVICE_DATA_TOPIC`: 设备数据主题，传输船舶传感器数据
2. `SNAPSHOT_LATEST_TOPIC`: 快照数据主题，传输船舶快照数据
3. `RAW_DATA_TOPIC`: 原始数据主题，传输船舶原始数据
4. `SHORE_SYNC_DATA_TOPIC`: 岸上同步数据主题，接收需要同步到船舶的数据

## 核心类和职责

1. **ConnectService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/ConnectService.java`
   - 职责: 初始化UDP服务器，解析UDP数据包，分发数据到不同处理服务

2. **NormalUdpClientHandler.java**
   - 路径: `src/main/java/com/xhjt/flystone/handler/NormalUdpClientHandler.java`
   - 职责: 处理UDP通道中接收到的数据

3. **SyncHandleService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/SyncHandleService.java`
   - 职责: 处理同步数据，将同步数据发送到船舶，处理同步响应

4. **DataHandleService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/DataHandleService.java`
   - 职责: 处理船舶设备数据，验证数据完整性，存储到Redis并发送到Kafka

5. **SnapshotHandleService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/SnapshotHandleService.java`
   - 职责: 处理船舶快照数据，验证数据完整性，存储到Redis并发送到Kafka

6. **SendService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/SendService.java`
   - 职责: 发送数据到船舶和Kafka消息队列

7. **ConsumerService.java**
   - 路径: `src/main/java/com/xhjt/flystone/service/ConsumerService.java`
   - 职责: 消费Kafka消息，处理岸上同步数据

## 结论

Flystone项目主要负责与船舶设备的通信，通过UDP协议接收船舶数据，并通过Kafka消息队列与其他系统组件进行数据交换。系统使用Redis缓存临时数据和状态信息，以确保数据的完整性和同步状态的跟踪。核心业务流程包括数据接收、处理、同步和响应，形成了一个完整的数据交换循环。