package com.xhjt.flystone.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.dctcore.commoncore.enums.PackageTypeEnum;
import com.xhjt.flystone.common.constants.RedisKeyConstants;
import com.xhjt.flystone.common.utils.SpringUtils;
import com.xhjt.flystone.feign.LaputaApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * class
 *
 * <AUTHOR>
 */
@Service
public class SyncHandleService {

    private Logger logger = LoggerFactory.getLogger(SyncHandleService.class);
    @Resource
    private ValueOperations<String, String> valueOperations;

    @Autowired
    private SendService sendService;
    @Autowired
    private LaputaApi laputaApi;

    void receiveMessage(TransferPackage transferPackage) {

        // 回复已经接收成功
        logger.info("同步数据接收成功111---{}", JSONObject.toJSONString(transferPackage));
        sendSuccess(transferPackage.getSn(), transferPackage.getCommandNum());
        if (valueOperations.get(getDataKey(transferPackage.getSn(), transferPackage.getCommandNum())) != null) {
            return;
        }

        // 操作数据库
        laputaApi.updateBySync(transferPackage);

        valueOperations.set(getDataKey(transferPackage.getSn(), transferPackage.getCommandNum()), JSONObject.toJSONString(transferPackage), 2, TimeUnit.HOURS);
    }

    void receiveSuccess(TransferPackage transferPackage){
        logger.info("同步数据接收成功回复---{}", JSONObject.toJSONString(transferPackage));
        valueOperations.set(getSuccessKey(transferPackage.getSn(), transferPackage.getCommandNum()), JSONObject.toJSONString(transferPackage), 2, TimeUnit.HOURS);
    }

    void receiveRawSuccess(TransferPackage transferPackage){
        logger.info("同步原始数据接收成功回复---{}", JSONObject.toJSONString(transferPackage));
        // 发送数据到kafka
        sendService.sendRawData2Kafka(transferPackage);
    }

    /**
     * 发送接收成功 到pazu
     *
     * @param sn
     * @param commandNum
     * @throws Exception
     */
    private void sendSuccess(String sn, Integer commandNum) {
        String sendStr = "2" + commandNum + ",SUCCESS";

        sendService.send2Ship(sn, sendStr, 5);
    }


    void sendSync2Ship(KafkaMessage kafkaMessage, Long offset) {
        TransferPackage transferPackage = new TransferPackage(offset, System.currentTimeMillis(), PackageTypeEnum.DATA_SYNC.getValue(), 0, kafkaMessage.getMsg(), kafkaMessage.getCode());

        String sendStr = "2" + JSONObject.toJSONString(transferPackage);

        logger.info("同步到船端----{}", sendStr);
        sendService.send2Ship(kafkaMessage.getSn(), sendStr, 2);

        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            try {
                Thread.sleep(1000 * 15);

                if (valueOperations.get(getSuccessKey(kafkaMessage.getSn(), transferPackage.getCommandNum())) == null) {
                    sendSync2Ship(kafkaMessage, offset);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }


    private String getDataKey(String sn, Integer commandNum) {
        return RedisKeyConstants.SHIP_SYNC_DATA + sn + "_" + commandNum;
    }

    private String getSuccessKey(String sn, Integer commandNum) {
        return RedisKeyConstants.SHIP_SYNC_SUCCESS + sn + "_" + commandNum;
    }
}
