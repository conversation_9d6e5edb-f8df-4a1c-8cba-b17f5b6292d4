package com.xhjt.flystone.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.flystone.common.constants.RedisKeyConstants;
import com.xhjt.flystone.common.utils.JsonUtil;
import com.xhjt.flystone.common.utils.NumUtils;
import com.xhjt.flystone.common.utils.SpringUtils;
import com.xhjt.flystone.domain.RedisObjVo;
import com.xhjt.flystone.feign.LaputaApi;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * 处理普通数据
 *
 * <AUTHOR>
 */
@Service
public class DataHandleService {

    private Logger logger = LoggerFactory.getLogger(DataHandleService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Resource
    private ListOperations<String, Object> listOperations;
    @Autowired
    private SendService sendService;
    @Autowired
    private LaputaApi laputaApi;
    /**
     * 检查数据的锁
     */
    private static ConcurrentMap<String, String> dataCheckLock = new ConcurrentHashMap<>();

    /**
     * 数据检查锁的key
     */
    private static final String LOSE_DATA_CHECK = "LOSE_DATA_CHECK-";


    /**
     * 移除已经被pazu删除的数据编号
     */
    boolean removeNonExistData(TransferPackage transferPackage) {
        if (transferPackage.getDeviceType() != -1) {
            return false;
        }
        logger.info("移除一条无效数据--，{}", JsonUtil.obj2String(transferPackage));
        String key = RedisKeyConstants.getKey(RedisKeyConstants.LOSE_DATA_LIST, transferPackage.getSn());
        listOperations.remove(key, 1L, transferPackage.getCommandNum());
        return true;
    }

    /**
     * 接收数据
     *
     * @param transferPackage
     */
    void receiveMessage(TransferPackage transferPackage) {
        String sn = transferPackage.getSn();
        String key;

        // 完整消息记录到redis,设置过期时间为3天
        key = RedisKeyConstants.getKey(RedisKeyConstants.RECEIVE_COMPLETE_DATA, sn, transferPackage.getCommandNum());
        valueOperations.set(key, transferPackage.getTime(), 3, TimeUnit.DAYS);

        // 发送数据到kafka
        sendService.sendData2Kafka(transferPackage);

        // 更新已记录的最大编号
        Integer maxReceiveNum = renewMaxReceiveNum(transferPackage.getSn(), transferPackage.getCommandNum());

        // 查看已检查过的编号
        key = RedisKeyConstants.getKey(RedisKeyConstants.MAX_CHECK_NUM, sn);
        Integer maxCheckNum = valueOperations.get(key) == null ? 0 : (Integer) valueOperations.get(key);
        if(maxCheckNum == 0){
            //如果redis没有则获取数据库的
            RedisObjVo selectObj = new RedisObjVo();
            selectObj.setRedisKey(RedisKeyConstants.getKey(RedisKeyConstants.MAX_CHECK_NUM, sn));
            selectObj.setSn(sn);
            RedisObjVo redisObjCheckVo = laputaApi.getRedisObj(selectObj);
             if (redisObjCheckVo != null){
                maxCheckNum = redisObjCheckVo.getValueLength();
            }else {
                //都没有的情况下默认赋值10万
                maxCheckNum = 0;
            }
        }
        // 小于已检查则可能是补过来的数据,需要清除丢数据记录
        if (transferPackage.getCommandNum() < maxCheckNum) {

            logger.info("完成一条补数据--，{}", JsonUtil.obj2String(transferPackage));
            key = RedisKeyConstants.getKey(RedisKeyConstants.LOSE_DATA_LIST, sn);
            listOperations.remove(key, 1L, transferPackage.getCommandNum());
        } else {

            logger.info("收到一条新数据--，{}", JsonUtil.obj2String(transferPackage));
            // 检查数据是否完整，不完整则补数据
            checkDataComplete(maxReceiveNum, maxCheckNum, transferPackage);
        }
    }

    /**
     * 更新最大编号
     *
     * @param sn
     * @param commandNum
     */
    private Integer renewMaxReceiveNum(String sn, Integer commandNum) {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.MAX_RECEIVE_NUM, sn);

        if (valueOperations.get(key) == null) {
            valueOperations.set(key, commandNum);
            return commandNum;
        }

        Integer maxNum = (Integer) valueOperations.get(key);

        if (commandNum > maxNum) {
            valueOperations.set(key, commandNum);
            maxNum = commandNum;
        }

        return maxNum;
    }


    /**
     * 检查数据的完整性
     *
     * @param maxReceiveNum
     * @param maxCheckNum
     * @param transferPackage
     */
    private void checkDataComplete(Integer maxReceiveNum, Integer maxCheckNum, TransferPackage transferPackage) {
        // 判断是否已有锁,有锁则说明已在检查
        if (dataCheckLock.containsKey(LOSE_DATA_CHECK + transferPackage.getSn())) {
            return;
        }

        // 记录的最大编号超过已检查编号一定数量之后，开始检查是否有丢数据
        int intervalNum = valueOperations.get(RedisKeyConstants.CHECK_INTERVAL_NUM) == null ? 100 : (Integer) valueOperations.get(RedisKeyConstants.CHECK_INTERVAL_NUM);
        if (maxReceiveNum - maxCheckNum < intervalNum) {
            return;
        }

        // 加锁，防止一直来检查数据
        dataCheckLock.put(LOSE_DATA_CHECK + transferPackage.getSn(), transferPackage.getSn());

        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            checkCompleteThread(maxReceiveNum, maxCheckNum, transferPackage);
        });
    }

    private void checkCompleteThread(Integer maxReceiveNum, Integer maxCheckNum, TransferPackage transferPackage) {
        String sn = transferPackage.getSn();
        String key;
        try {
            int offsetNum = maxCheckNum;
            while (offsetNum < maxReceiveNum) {
                offsetNum++;
                key = RedisKeyConstants.getKey(RedisKeyConstants.RECEIVE_COMPLETE_DATA, sn, offsetNum);

                if (valueOperations.get(key) != null) {
                    continue;
                }

                // 记录丢失的数据
                key = RedisKeyConstants.getKey(RedisKeyConstants.LOSE_DATA_LIST, sn);
                listOperations.rightPush(key, offsetNum);
            }
            // 更新已查询的最大编码数据
            key = RedisKeyConstants.getKey(RedisKeyConstants.MAX_CHECK_NUM, sn);
            valueOperations.set(key, offsetNum);

            // 发送UDP补数据
            repairData(sn);
        } catch (Exception e) {
            logger.error("补数据出错--{}", e);
            removeLoseDataCheckLock(sn);
        } finally {
            logger.info("补数据成功");
            removeLoseDataCheckLock(sn);

            // TODO 记录最大编码和最大检查编码到mysql数据库
        }
    }

    /**
     * 补数据
     *
     * @param sn
     */
    private void repairData(String sn) {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.LOSE_DATA_LIST, sn);
        // 链表中没有数据则不需要补数据
        if (redisTemplate.hasKey(key) == null || listOperations.size(key) == 0) {
            return;
        }

        // 获取链表中数据并存入待发送set
        Set<Integer> numSets = Sets.newHashSet();
        for (int i = 0; i < 240; i++) {
            numSets.add((Integer) listOperations.rightPopAndLeftPush(key, key));
        }

        // 获取byteBuf
        List<ByteBuf> byteBufList = Lists.newArrayList();
        getByteBufList(new ArrayList<>(numSets), byteBufList);

        //数据写入连接通道
        sendService.writeRequest(sn, byteBufList);
    }

    /**
     * 获取byteBuf
     *
     * @param numList
     * @param byteBufList
     * @return
     */
    private void getByteBufList(List<Integer> numList, List<ByteBuf> byteBufList) {
        byte[] allBytes = NumUtils.list2ByteArray(numList);
        byte[] bytes = new byte[allBytes.length + 1];
        // 添加标识
        byte[] type = "0".getBytes();
        bytes[0] = type[0];

        System.arraycopy(allBytes, 0, bytes, 1, allBytes.length);

        byteBufList.add(Unpooled.copiedBuffer(bytes));
    }

    /**
     * 去除检查锁
     *
     * @param sn
     */
    private void removeLoseDataCheckLock(String sn) {
        dataCheckLock.remove(LOSE_DATA_CHECK + sn);
    }
}

