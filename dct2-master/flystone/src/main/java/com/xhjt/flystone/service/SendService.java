package com.xhjt.flystone.service;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.flystone.common.domain.ChannelInfo;
import com.xhjt.flystone.common.utils.ChannelInfoUtil;
import com.xhjt.flystone.common.utils.JsonUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.net.InetSocketAddress;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * class
 *
 * <AUTHOR>
 */
@Service
public class SendService {

    private Logger logger = LoggerFactory.getLogger(SendService.class);

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 把需要发送的数据写入udp通道中，此通道是保存pazu发送过来的通道
     *
     * @param sn
     * @param list
     */
    void writeRequest(String sn, List<ByteBuf> list) {
        try {

            ChannelInfo channelInfo = ChannelInfoUtil.getChannel(sn);
            if (channelInfo == null || channelInfo.getCtx() == null
                    || channelInfo.getIp() == null || channelInfo.getPort() == null) {
                logger.info("ctx为---------null------------{}", sn);
                return;
            }

            //UDP广播
            for (ByteBuf byteBuf : list) {
                channelInfo.getCtx().writeAndFlush(new DatagramPacket(byteBuf, new InetSocketAddress(
                        channelInfo.getIp(), channelInfo.getPort())));

                Thread.sleep(1000);
            }
        } catch (Exception e) {
            logger.error("UDP发送异常---，{}", e);
        }
    }

    void send2Ship(String sn, String message, Integer num) {
        // 发送
        List<ByteBuf> list = Stream.iterate(0, i -> i + 1).limit(num).map(i -> Unpooled.copiedBuffer(message, CharsetUtil.UTF_8))
                .collect(Collectors.toList());

        writeRequest(sn, list);
    }

    /**
     * 发送数据到kafka
     *
     * @param transferPackage
     */
    void sendData2Kafka(TransferPackage transferPackage) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send("DEVICE_DATA_TOPIC", JsonUtil.obj2String(transferPackage));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", "DEVICE_DATA_TOPIC", ex.getMessage()));
    }

    /**
     * 发送原始数据到kafka
     *
     * @param transferPackage
     */
    void sendRawData2Kafka(TransferPackage transferPackage) {
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send("RAW_DATA_TOPIC", JsonUtil.obj2String(transferPackage));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", "RAW_DATA_TOPIC", ex.getMessage()));
    }

    /**
     * 发送数据到kafka
     *
     * @param transferPackage
     */
    void sendSnapshot2Kafka(TransferPackage transferPackage) {
        transferPackage.setMessage("");
        //发送消息，topic不存在将自动创建新的topic
        ListenableFuture<SendResult<String, String>> listenableFuture = kafkaTemplate.send("SNAPSHOT_LATEST_TOPIC", JsonUtil.obj2String(transferPackage));
        //添加成功发送消息的回调和失败的回调
        listenableFuture.addCallback(
                result -> {
                },
                ex -> logger.info("send message to {} failure,error message:{}", "SNAPSHOT_LATEST_TOPIC", ex.getMessage()));
    }

}
