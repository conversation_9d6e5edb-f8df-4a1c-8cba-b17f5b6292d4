package com.xhjt.flystone.service;

import com.xhjt.dctcore.commoncore.domain.KafkaMessage;
import com.xhjt.flystone.common.utils.JsonUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * class
 *
 * <AUTHOR>
 */
@Component
public class ConsumerService {

    private Logger logger = LoggerFactory.getLogger(ConsumerService.class);

    @Autowired
    private SyncHandleService syncHandleService;


    /**
     * 接收topic消息，并把数据存入redis中
     *
     * @param record
     */
    @KafkaListener(topics = {"SHORE_SYNC_DATA_TOPIC"}, groupId = "flystone_v1")
    public void syncDataConsumer(ConsumerRecord<?, ?> record) {
        Optional<?> sourceMessage = Optional.ofNullable(record.value());
        if (sourceMessage.isPresent()) {
            Object message = sourceMessage.get();
            KafkaMessage kafkaMessage = JsonUtil.string2Obj(message.toString(), KafkaMessage.class);

            syncHandleService.sendSync2Ship(kafkaMessage, record.offset());
        }
    }
}
