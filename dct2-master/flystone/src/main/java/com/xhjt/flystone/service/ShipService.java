package com.xhjt.flystone.service;

import com.xhjt.flystone.common.constants.RedisKeyConstants;
import com.xhjt.flystone.common.utils.JsonUtil;
import com.xhjt.flystone.domain.ShipEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 船只管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ShipService {
    private Logger logger = LoggerFactory.getLogger(ShipService.class);

    @Resource
    private ValueOperations<String, String> valueOperations;

    @Autowired
    JdbcTemplate jdbcTemplate;


    /**
     * 查询所有
     *
     * @return
     */
    public List<ShipEntity> queryAll() {
        String sql = "SELECT * FROM ship";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShipEntity.class));
    }

    /**
     * 查询所有启用的
     *
     * @return
     */
    List<String> queryEnableSnList() {

        String snListStr = valueOperations.get(RedisKeyConstants.ALL_ENABLE_SHIP_SN);
        if (StringUtils.isNotBlank(snListStr)) {
            return JsonUtil.object2Obj(snListStr, List.class, String.class);
        }

        return renewEnableSnList();
    }

    /**
     * 更新redis中sn集合
     *
     * @throws Exception
     */
    public List<String> renewEnableSnList() {

        String sql = "SELECT sn FROM ship WHERE status = 1";
        List<String> snList = jdbcTemplate.queryForList(sql, String.class);

        if (snList.size() == 0) {
            snList.add("TEMP");
        }

        logger.info("更新SN集合成功----{}", JsonUtil.obj2String(snList));

        valueOperations.set(RedisKeyConstants.ALL_ENABLE_SHIP_SN, JsonUtil.obj2String(snList));

        return snList;
    }


}
