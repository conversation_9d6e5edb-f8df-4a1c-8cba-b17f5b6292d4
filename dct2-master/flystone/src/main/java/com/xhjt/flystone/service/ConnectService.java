package com.xhjt.flystone.service;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.dctcore.commoncore.enums.PackageTypeEnum;
import com.xhjt.flystone.common.utils.JsonUtil;
import com.xhjt.flystone.common.utils.NumUtils;
import com.xhjt.flystone.common.utils.SpringUtils;
import com.xhjt.flystone.server.UdpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * class
 *
 * <AUTHOR>
 */
@Service
public class ConnectService {

    private Logger logger = LoggerFactory.getLogger(ConnectService.class);
    /**
     * 岸上UPD监听 端口
     */
    @Value("${flystone.udpPort}")
    private Integer udpPort;

    @Resource
    private DataHandleService dataHandleService;
    @Resource
    private SnapshotHandleService snapshotHandleService;
    @Resource
    private SyncHandleService syncHandleService;

    @Resource
    private ShipService shipService;
    @Resource
    private DeviceService deviceService;


    /**
     * 创建UDP接收服务
     */
    public void initUdpServer() {
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            new UdpServer(udpPort);
        });
    }


    /**
     * 处理接收到的消息
     *
     * @param transferPackage
     */
    public void handleMessage(TransferPackage transferPackage) {
        try {
            if (transferPackage == null) {
                return;
            }
            // 保持连接的信息,不做处理
            if (PackageTypeEnum.CONNECT_KEEP.getValue().equals(transferPackage.getPackageType())) {
                logger.info("接到数据,--保持连接---{}", JsonUtil.obj2String(transferPackage));
                return;
            }
            // 判断sn是否有效
            List<String> snList = shipService.queryEnableSnList();
            if (!snList.contains(transferPackage.getSn())) {
                logger.info("接到数据,--SN无效---{}", JsonUtil.obj2String(transferPackage));
                return;
            }

            // 处理传感器数据
            if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
                if (dataHandleService.removeNonExistData(transferPackage)) {
                    return;
                }
                // 判断设备是否有效
                List<String> codeList = deviceService.queryEnableCodeList();
                if (!codeList.contains(transferPackage.getSn() + "_" + transferPackage.getDeviceCode())) {
                    logger.info("接到数据,--设备无效---{}", JsonUtil.obj2String(transferPackage));
                    return;
                }
                dataHandleService.receiveMessage(transferPackage);
            }

            // 处理快照数据
            if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
                snapshotHandleService.receiveMessage(transferPackage);
            }

            // 处理同步数据
            if (PackageTypeEnum.DATA_SYNC.getValue().equals(transferPackage.getPackageType())) {
                syncHandleService.receiveMessage(transferPackage);
            }

            // 处理同步数据成功回复
            if (PackageTypeEnum.DATA_SYNC_SUCCESS.getValue().equals(transferPackage.getPackageType())) {
                syncHandleService.receiveSuccess(transferPackage);
            }

            // 处理原始数据
            if (transferPackage.getPackageType()==5) {
                syncHandleService.receiveRawSuccess(transferPackage);
            }

        } catch (Exception e) {
            logger.error("处理数据失败，--{}", e);
        }
    }

    /**
     * 解析传输的UDP包
     * @param msg
     * @return
     */
    public TransferPackage analysisPackage(byte[] msg) throws Exception {
        int length = msg.length;
        if (length < 20) {
            return null;
        }

        TransferPackage transferPackage = new TransferPackage();

        int offset = 0;

        // 船只序列号
        transferPackage.setSn(new String(NumUtils.splitBytes(msg, offset, 6)));
        offset += 6;

        // 设备类型
        transferPackage.setPackageType((int) NumUtils.splitByte(msg, offset));
        offset++;

        // 统一编码
        transferPackage.setCommandNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        //时间戳
        transferPackage.setTime(NumUtils.bytesToLong(NumUtils.splitBytes(msg, offset, 8)));
        offset += 8;

        // 是否补发
        transferPackage.setIsRepair((int) NumUtils.splitByte(msg, offset));
        offset++;

        if(PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())){
            // 设备类型
            transferPackage.setDeviceType((int) NumUtils.splitByte(msg, offset));
            offset++;
        }

        // 设备编码
        transferPackage.setDeviceCode(new String(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        if(PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())){
            // 拆包总数
            transferPackage.setUnpackingTotal(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;

            // 拆包编号
            transferPackage.setUnpackingNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;
        }

        // 消息主体
        transferPackage.setMessage(new String(NumUtils.splitBytes(msg, offset, length - offset)));

        return transferPackage;
    }
}
