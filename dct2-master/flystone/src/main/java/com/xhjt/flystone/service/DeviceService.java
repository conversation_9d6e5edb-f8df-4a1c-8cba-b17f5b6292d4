package com.xhjt.flystone.service;

import com.xhjt.flystone.common.constants.RedisKeyConstants;
import com.xhjt.flystone.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 设备管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceService {

    private Logger logger = LoggerFactory.getLogger(DeviceService.class);

    @Resource
    private ValueOperations<String, String> valueOperations;

    @Autowired
    JdbcTemplate jdbcTemplate;


    /**
     * 查询所有启用的
     *
     * @return
     */
    public List<String> queryEnableCodeList() {

        String codeListStr = valueOperations.get(RedisKeyConstants.ALL_ENABLE_DEVICE_CODE);
        if (StringUtils.isNotBlank(codeListStr)) {
            return JsonUtil.object2Obj(codeListStr, List.class, String.class);
        }

        return renewEnableCodeList();
    }

    /**
     * 更新redis中设备编码集合
     *
     * @throws Exception
     */
    public List<String> renewEnableCodeList() {

        String sql = "SELECT CONCAT(sn,'_',code) FROM device WHERE enable = 1";
        List<String> codeList = jdbcTemplate.queryForList(sql, String.class);

        if (codeList.size() == 0) {
            codeList.add("TEMP");
        }

        logger.info("更新设备编码集合成功----{}", JsonUtil.obj2String(codeList));

        valueOperations.set(RedisKeyConstants.ALL_ENABLE_DEVICE_CODE, JsonUtil.obj2String(codeList));

        return codeList;
    }


}
