package com.xhjt.flystone.init;

import com.xhjt.flystone.common.utils.SpringUtils;
import com.xhjt.flystone.service.ConnectService;
import com.xhjt.flystone.service.DeviceService;
import com.xhjt.flystone.service.ShipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/5 23:55
 */
@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void run(String... args) throws Exception {
        logger.info("系统基础功能初始化开始...");

        // 更新数据库中的sn号信息到缓存
        ShipService shipService = SpringUtils.getBean(ShipService.class);
        shipService.renewEnableSnList();
        // 更新数据库中的sn号信息到缓存
        DeviceService deviceService = SpringUtils.getBean(DeviceService.class);
        deviceService.renewEnableCodeList();

        //TODO 更新一些redis中的数据

        ConnectService connectService = SpringUtils.getBean(ConnectService.class);
        connectService.initUdpServer();

        logger.info("系统基础功能初始化结束...");
    }
}
