package com.xhjt.flystone.handler;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.flystone.common.utils.ChannelInfoUtil;
import com.xhjt.flystone.common.utils.SpringUtils;
import com.xhjt.flystone.service.ConnectService;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * class
 *
 * <AUTHOR>
 */
public class NormalUdpClientHandler extends ChannelInboundHandlerAdapter {

    protected Logger logger = LoggerFactory.getLogger(NormalUdpClientHandler.class);


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        try {
            DatagramPacket packet = (DatagramPacket) msg;
            ByteBuf byteBuf = packet.content();
            byte[] bytes = new byte[byteBuf.readableBytes()];
            byteBuf.readBytes(bytes);

            // 根据船上发送来的数据，更新pazu的ip和port
            // UDP获取方式与TCP获取方式不同
            InetSocketAddress remoteAddress = packet.sender();
            String ip = remoteAddress.getAddress().getHostAddress();
            int port = remoteAddress.getPort();

            ConnectService connectService = SpringUtils.getBean(ConnectService.class);
            TransferPackage transferPackage = connectService.analysisPackage(bytes);
            if (transferPackage == null) {
                return;
            }

            // 更新通道信息
            ChannelInfoUtil.renewChannel(transferPackage.getSn(), ctx, ip, port);

            // 处理接收到的消息
            connectService.handleMessage(transferPackage);

        } finally {
            ReferenceCountUtil.release(msg);
        }
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        //未打印堆栈具体信息，只显示错误message
        String uuid = String.valueOf(ctx.channel().id());
        logger.error("ChannelInboundHandler_Exception：UDP广播异常：{}，ip：{}，error：{}", new Object[]{uuid, "", cause});
        ctx.close();
    }
}
