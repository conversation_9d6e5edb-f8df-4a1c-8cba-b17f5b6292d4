package com.xhjt.flystone.feign;

import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.flystone.domain.RedisObjVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * class
 *
 * <AUTHOR>
 */
@FeignClient(name = "laputa", url = "${feign.laputaUrl}")
public interface LaputaApi {


    /**
     * 设备同步
     *
     * @param transferPackage
     * @return
     */
    @PostMapping("/api/sync/updateBySync")
    void updateBySync(@RequestBody TransferPackage transferPackage);

    /**
     * 获取redis对象
     *
     * @param selectObj
     * @return
     */
    @PostMapping("/api/sync/getRedisObj")
    RedisObjVo getRedisObj(@RequestBody RedisObjVo selectObj);
}
