package com.xhjt.flystone.server;

import com.xhjt.flystone.handler.NormalUdpClientHandler;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 创建UDP服务端
 *
 * <AUTHOR>
 */
public class UdpServer {

    private Logger logger = LoggerFactory.getLogger(UdpServer.class);

    private int port;

    public UdpServer(int port) {
        this.port = port;
        bind();
    }

    private void bind() {
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .handler(new NormalUdpClientHandler());

            ChannelFuture f = bootstrap.bind(this.port).sync();

            logger.info("UDP服务已启动，端口：{}", this.port);

            // 等待服务器 socket 关闭 。
            // 在这个例子中，这不会发生，但你可以优雅地关闭你的服务器。
            f.channel().closeFuture().sync();

        } catch (Exception e) {
            logger.error("启动Netty服务异常，异常信息：" + e.getMessage());
            e.printStackTrace();
        } finally {
            group.shutdownGracefully();
        }
    }


}
