package com.xhjt.flystone.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xhjt.dctcore.commoncore.domain.transfer.TransferPackage;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.flystone.common.constants.RedisKeyConstants;
import com.xhjt.flystone.common.utils.JsonUtil;
import com.xhjt.flystone.common.utils.ListUtil;
import com.xhjt.flystone.common.utils.SpringUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.util.CharsetUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static org.springframework.util.FileCopyUtils.BUFFER_SIZE;

/**
 * 快照数据处理
 *
 * <AUTHOR>
 */
@Service
public class SnapshotHandleService {

    private Logger logger = LoggerFactory.getLogger(SnapshotHandleService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Autowired
    private SendService sendService;

    private static final Integer HALF_HOUR = 30 * 60 * 1000;
    private static final Integer FIVE_MINUTES = 5 * 60 * 1000;

    /**
     * 检查快照数据的锁
     */
    private static ConcurrentMap<String, String> packageCheckLock = new ConcurrentHashMap<>();
    /**
     * 数据检查锁的key
     */
    private static final String DATA_COMPLETE_CHECK = "DATA_COMPLETE_CHECK-";

    /**
     * 快照存放地址
     */
    @Value("${flystone.snapshotPath}")
    private String snapshotPath;


    /**
     * 接收数据
     *
     * @param transferPackage
     */
    void receiveMessage(TransferPackage transferPackage) {
        if (transferPackage.getIsRepair() == 2) {
            logger.info("快照,询问是否收集成功--，{}", JsonUtil.obj2String(transferPackage));
            checkComplete4Ask(transferPackage);
            return;
        }

        if (transferPackage.getIsRepair() == 1) {
            logger.info("快照,补数据--，channelCode:{}--commandNum:{}--unpackingNum:{}--unpackingTotal:{}", transferPackage.getDeviceCode(), transferPackage.getCommandNum(), transferPackage.getUnpackingNum(), transferPackage.getUnpackingTotal());
        }
        if (transferPackage.getIsRepair() == 0) {
            logger.info("快照,新数据--，channelCode:{}--commandNum:{}--unpackingNum:{}--unpackingTotal:{}", transferPackage.getDeviceCode(), transferPackage.getCommandNum(), transferPackage.getUnpackingNum(), transferPackage.getUnpackingTotal());
        }

        // 保存数据到redis，等待所有包接收完整
        String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_RECEIVE_TEMPORARY, transferPackage.getSn(), transferPackage.getCommandNum());
        Set<TransferPackage> messageSet;
        if (!redisTemplate.hasKey(key)) {
            messageSet = Sets.newHashSet(transferPackage);
        } else {
            messageSet = JsonUtil.object2Obj(valueOperations.get(key), Set.class, TransferPackage.class);
            messageSet.add(transferPackage);
        }
        valueOperations.set(key, messageSet, 4, TimeUnit.HOURS);

        // 更新该编号的最新获取数据时间
        key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
        valueOperations.set(key, System.currentTimeMillis(), 4, TimeUnit.HOURS);

        //检查数据是否已经接收完整
        checkPackage(transferPackage);
    }

    private void checkPackage(TransferPackage transferPackage) {
        // 有拆包的需要做合包，合包前需要判断是否完整的接收到所有子包
        // 一个完整的包只启动一个线程，通过加锁来实现
        // 判断是否已有锁
        if (packageCheckLock.containsKey(DATA_COMPLETE_CHECK + transferPackage.getSn() + "-" + transferPackage.getCommandNum())) {
            return;
        }
        packageCheckLock.put(DATA_COMPLETE_CHECK + transferPackage.getSn() + "-" + transferPackage.getCommandNum(), transferPackage.getCommandNum().toString());

        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            try {
                checkPackageThread(transferPackage);
            } catch (Exception e) {
                logger.error("异常", e);
            } finally {
                removeCheckLock(transferPackage);
            }
        });
    }

    /**
     * 判断包是否已接收完整
     * 由于传输需要时间，所以每200毫秒轮询redis，n秒后退出轮询，定为丢包
     *
     * @param transferPackage
     * @return
     * @throws InterruptedException
     */
    private Boolean checkPackageComplete(TransferPackage transferPackage) throws Exception {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_RECEIVE_TEMPORARY, transferPackage.getSn(), transferPackage.getCommandNum());

        if (!redisTemplate.hasKey(key)) {
            valueOperations.set(key, Sets.newHashSet(), 4, TimeUnit.HOURS);
            logger.info("快照,channelCode:{}--commandNum:{}已收集长度有--null", transferPackage.getDeviceCode(), transferPackage.getCommandNum());
            return false;
        }
        Set<TransferPackage> set = JsonUtil.object2Obj(valueOperations.get(key), Set.class, TransferPackage.class);
        logger.info("快照,channelCode:{}--commandNum:{}已收集长度有--{}", transferPackage.getDeviceCode(), transferPackage.getCommandNum(), set.size());

        // 已经获取到足够数量的快照包，直接返回true
        if (transferPackage.getUnpackingTotal() <= set.size()) {
            return true;
        }

        // 最新接收到数据的时间
        key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
        long lastTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;
        // 最新补数据的时间
        key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_REPAIR_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
        long lastRepairTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;

        if (System.currentTimeMillis() - lastTime < 25 * 1000 || System.currentTimeMillis() - lastRepairTime < 20 * 1000) {
            Thread.sleep(2000);
            return checkPackageComplete(transferPackage);
        }

        return false;
    }

    /**
     * 检查数据是否已经接收完整
     *
     * @param transferPackage
     */
    private void checkPackageThread(TransferPackage transferPackage) throws Exception {

        Boolean isComplete = checkPackageComplete(transferPackage);

        //完整的包则输出
        if (isComplete) {
            logger.info("快照,channelCode:{}--commandNum:{},完整的包", transferPackage.getDeviceCode(), transferPackage.getCommandNum());
            // 合并包
            generatePackage(transferPackage);
            // 更新最新快照时间
            renewLatestPictureTime(transferPackage.getSn(), transferPackage.getDeviceCode(), transferPackage.getTime());
            // 更新最新快照时间
            sendService.sendSnapshot2Kafka(transferPackage);
            // 接收到完整的包反馈到pazu
            sendSuccess(transferPackage.getSn(), transferPackage.getCommandNum(), transferPackage.getDeviceCode());
        }

        // 丢包则需要走补包流程
        if (!isComplete) {
            Set<String> sendList = getLosePackage(transferPackage);
            logger.info("快照,有丢包，开始补包111,channelCode:{}--commandNum:{}，----{}", transferPackage.getDeviceCode(), transferPackage.getCommandNum(), JsonUtil.obj2String(sendList));
            sendRepair(sendList, transferPackage);
        }
    }


    /**
     * 判断快照是否接收完整，完整则发送成功标记
     *
     * @param transferPackage
     */
    private void checkComplete4Ask(TransferPackage transferPackage) {
        try {
            String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_RECEIVE_TEMPORARY, transferPackage.getSn(), transferPackage.getCommandNum());

            boolean isComplete = false;
            if (redisTemplate.hasKey(key)) {
                Set<TransferPackage> set = JsonUtil.object2Obj(valueOperations.get(key), Set.class, TransferPackage.class);
                isComplete = set.size() >= transferPackage.getUnpackingTotal();
            }

            if (isComplete) {
                // 移除合包锁，并进行补包或者传输完成标识
                removeCheckLock(transferPackage);
                // 接收到完整的包反馈到pazu
                sendSuccess(transferPackage.getSn(), transferPackage.getCommandNum(), transferPackage.getDeviceCode());
                return;
            }

            // 最新补数据的时间
            key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_REPAIR_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
            long lastRepairTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;
            // 最新接收到数据的时间
            key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
            long lastTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;

            // 时间小于5分钟，则可能还在传输快照中
            if (System.currentTimeMillis() - lastRepairTime < FIVE_MINUTES || System.currentTimeMillis() - lastTime < FIVE_MINUTES) {
                return;
            }

            // 移除检查锁
            removeCheckLock(transferPackage);

            // 时间大于半小时，则直接丢弃，开始接收新快照
            if (System.currentTimeMillis() - lastTime > HALF_HOUR && System.currentTimeMillis() - lastRepairTime > HALF_HOUR) {
                sendSuccess(transferPackage.getSn(), transferPackage.getCommandNum(), transferPackage.getDeviceCode());
            }

            // 时间小于半小时，再开一个线程来补数据
            if (System.currentTimeMillis() - lastRepairTime > FIVE_MINUTES && System.currentTimeMillis() - lastRepairTime < HALF_HOUR) {
                checkPackage(transferPackage);
            }

        } catch (Exception e) {
            removeCheckLock(transferPackage);
            logger.error("异常", e);
        }
    }

    /**
     * 获取丢包的编号（拆包）
     *
     * @param transferPackage
     * @return
     * @throws Exception
     */
    private Set<String> getLosePackage(TransferPackage transferPackage) throws Exception {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_RECEIVE_TEMPORARY, transferPackage.getSn(), transferPackage.getCommandNum());
        if (!redisTemplate.hasKey(key)) {
            return Sets.newHashSet();
        }

        Set<TransferPackage> set = JsonUtil.object2Obj(valueOperations.get(key), Set.class, TransferPackage.class);

        List<Integer> existList = ListUtil.fetchIntegerFieldValueList(Lists.newArrayList(set), "unpackingNum");

        List<Integer> loseList = Stream.iterate(0, i -> i + 1)
                .limit(transferPackage.getUnpackingTotal())
                .filter(i -> !existList.contains(i))
                .collect(Collectors.toList());

        Set<String> sendStrSet = Sets.newHashSet();

        if (loseList.size() == 0) {
            return sendStrSet;
        }

        // 把需要补的数据转化为 {commandNum,code,unpackingTotal,unpackingNum,unpackingNum,unpackingNum...}
        StringBuffer sb = new StringBuffer();
        for (Integer num : loseList) {
            if (sb.length() >= 500) {
                String str = sb.toString();
                sendStrSet.add(str);
                sb = new StringBuffer();
            }
            if (sb.length() == 0) {
                sb.append(transferPackage.getCommandNum()).append(",");
                sb.append(transferPackage.getDeviceCode()).append(",");
                sb.append(transferPackage.getUnpackingTotal()).append(",");
            }
            sb.append(num).append(",");
        }
        sendStrSet.add(sb.toString());

        return sendStrSet;
    }

    /**
     * 更新最新快照时间
     *
     * @param sn
     * @param time
     */
    private void renewLatestPictureTime(String sn, String channelCode, Long time) {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.LATEST_PICTURE_DATE, sn, channelCode);
        if (!redisTemplate.hasKey(key)) {
            valueOperations.set(key, time);
            return;
        }

        if (time > (Long) valueOperations.get(key)) {
            valueOperations.set(key, time);
        }
    }

    /**
     * 发送接收成功 到pazu
     *
     * @param sn
     * @param commandNum
     * @throws Exception
     */
    private void sendSuccess(String sn, Integer commandNum, String channelCode) {
        String sendStr = "1" + commandNum + "," + channelCode + ",SUCCESS";

        sendService.send2Ship(sn, sendStr, 5);
    }


    /**
     * 发送UDP补包请求
     */
    public void sendRepair(Set<String> list, TransferPackage transferPackage) {
        try {
            String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_REPAIR_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
            valueOperations.set(key, System.currentTimeMillis(), 4, TimeUnit.HOURS);

            List<ByteBuf> byteBufList = Lists.newArrayList();
            for (String sendStr : list) {
                if (StringUtils.isBlank(sendStr)) {
                    continue;
                }
                // 发送快照接收成功语句，格式为 1：快照类型，+ ..
                byteBufList.add(Unpooled.copiedBuffer("1" + sendStr, CharsetUtil.UTF_8));
            }

            // 发送
            sendService.writeRequest(transferPackage.getSn(), byteBufList);

            // 最新接收到数据的时间
            key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_NEWEST_TIME, transferPackage.getSn(), transferPackage.getCommandNum());
            long lastTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;
            while (System.currentTimeMillis() - lastTime < 10 * 1000) {
                Thread.sleep(2 * 1000);
                lastTime = redisTemplate.hasKey(key) ? (Long) valueOperations.get(key) : 0L;
            }
            // 时间超过2分钟释放线程
            if (System.currentTimeMillis() - lastTime < 2 * 60 * 1000) {
                checkPackageThread(transferPackage);
            }
        } catch (Exception e) {
            logger.error("异常", e);
        }
    }

    /**
     * 合并包，解压
     *
     * @param transferPackage
     */
    private void generatePackage(TransferPackage transferPackage) throws Exception {
        String key = RedisKeyConstants.getKey(RedisKeyConstants.SNAPSHOT_RECEIVE_TEMPORARY, transferPackage.getSn(), transferPackage.getCommandNum());
        if (!redisTemplate.hasKey(key)) {
            return;
        }

        Set<TransferPackage> msgSet = JsonUtil.object2Obj(valueOperations.get(key), Set.class, TransferPackage.class);

        String msg = getCompleteMessage(msgSet);

        // 验证文件夹路径是否存在
        String folderPath = getFinalPath(transferPackage);
        File folderFile = new File(folderPath);
        if (!folderFile.exists()) {
            folderFile.mkdirs();
        }

        //合并成zip
        String zipPath = folderPath + "/" + transferPackage.getTime() + ".zip";
        File zipFile = new File(zipPath);
        generateZip(msg, zipFile);
        //解压
        unZip(zipFile, folderPath);
        // 删除ZIP文件
        if (zipFile.delete()) {
            logger.info("快照，ZIP文件删除成功");
        }
    }


    /**
     * 获取完整的数据
     *
     * @param messageList
     */
    private String getCompleteMessage(Set<TransferPackage> messageList) {
        logger.info("快照，messageList长度，{}", messageList.size());
        String[] msgArray = new String[messageList.size()];

        for (TransferPackage transferPackage : messageList) {
            msgArray[transferPackage.getUnpackingNum()] = transferPackage.getMessage();
        }

        StringBuilder message = new StringBuilder();
        for (String msg : msgArray) {
            message.append(msg);
        }
        return message.toString();
    }

    /**
     * 字符串合成zip压缩包
     */
    private void generateZip(String imgStr, File zipFile) throws Exception {
        FileOutputStream ftpOutStream = new FileOutputStream(zipFile);
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] appByte = decoder.decodeBuffer(imgStr);
        ftpOutStream.write(appByte);
        ftpOutStream.flush();
        ftpOutStream.close();
    }

    /**
     * 获取图片的保持文件目录
     *
     * @param transferPackage
     * @return
     */
    private String getFinalPath(TransferPackage transferPackage) {
        return snapshotPath + "/"
                + transferPackage.getSn() + "/"
                + transferPackage.getDeviceCode() + "/"
                + DateUtils.getDateToString(transferPackage.getTime(), DateUtils.DATE_PATTERN_NONE);
    }

    /**
     * 解压
     */
    private void unZip(File srcFile, String destDirPath) throws RuntimeException {

        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(srcFile);
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                // 如果是文件夹，就创建个文件夹
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + "/" + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    // 如果是文件，就先创建一个文件，然后用io流把内容copy过去
                    File targetFile = new File(destDirPath + "/" + entry.getName());
                    // 保证这个文件的父文件夹必须要存在
                    if (!targetFile.getParentFile().exists()) {
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    // 将压缩文件内容写入到这个文件中
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[BUFFER_SIZE];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    fos.close();
                    is.close();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("解压错误---{}", e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * //判断文件夹是否存在,不存在，则创建
     *
     * @param dirPath
     */
    private void isChartPathExist(String dirPath) {
        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * 复制快照
     *
     * @param srcPath
     * @param destPath
     * @throws IOException
     */
    private void copyFile(String srcPath, String destPath) throws IOException {
        // 打开输入流
        FileInputStream fis = new FileInputStream(srcPath);
        // 打开输出流
        FileOutputStream fos = new FileOutputStream(destPath);

        // 读取和写入信息
        int len = 0;
        // 创建一个字节数组，当做缓冲区
        byte[] b = new byte[1024];
        while ((len = fis.read(b)) != -1) {
            fos.write(b, 0, len);
        }

        // 关闭流  先开后关  后开先关
        fos.close();
        fis.close();
    }

    /**
     * 获取时间
     */
    private String getDate() {
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(d);
    }


    /**
     * 去除检查锁
     *
     * @param transferPackage
     */
    private void removeCheckLock(TransferPackage transferPackage) {
        packageCheckLock.remove(DATA_COMPLETE_CHECK + transferPackage.getSn() + "-" + transferPackage.getCommandNum());
    }
}
