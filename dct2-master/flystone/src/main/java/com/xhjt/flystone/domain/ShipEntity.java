package com.xhjt.flystone.domain;


import com.xhjt.flystone.common.domain.BaseEntity;

/**
 * 船只信息
 *
 * <AUTHOR>
 */
public class ShipEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long shipId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 船只名称
     */
    private String name;

    /**
     * 船只编码
     */
    private String code;

    /**
     * sn
     */
    private String sn;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;


    public Long getShipId() {
        return shipId;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
