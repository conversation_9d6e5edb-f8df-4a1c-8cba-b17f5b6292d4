server:
  # 服务器的HTTP端口，默认为8080
  port: 9993
  servlet:
    # 应用的访问路径
    context-path: /
    tomcat:
      # tomcat的URI编码
      uri-encoding: UTF-8
      # tomcat最大线程数，默认为200
      max-threads: 800
      # Tomcat启动初始化的线程数，默认值25
      min-spare-threads: 30

# 日志配置
logging:
  level:
    com.xhjt.tcpserver: debug
    org.springframework: warn

spring:
  devtools:
    enabled: true

  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 密码
    password: 123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
    database: 4
  # kafka 配置
  kafka:
    bootstrap-servers: http://***************:9092
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  # 数据源配置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: root
    password: 123456

# 岸上UDP接收配置
flystone:
  # 端口
  udpPort: 10003
  #快照存放地址
  snapshotPath: /home/<USER>/snapshot/

#Feign的请求Url设置
feign:
  laputaUrl: http://localhost:9990